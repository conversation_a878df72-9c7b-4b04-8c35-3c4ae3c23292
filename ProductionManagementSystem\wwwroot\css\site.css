html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Дополнительные стили для системы управления производством */

/* Цветные границы для карточек */
.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
  border-left: 0.25rem solid #e74a3b !important;
}

/* Стили для статусов */
.status-pending {
  color: #f6c23e;
}

.status-in-progress {
  color: #36b9cc;
}

.status-completed {
  color: #1cc88a;
}

.status-cancelled {
  color: #e74a3b;
}

/* Анимации для интерактивных элементов */
.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Стили для таблиц */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

/* Индикаторы прогресса */
.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

/* Стили для уведомлений */
.alert {
  border-radius: 0.5rem;
}

/* Стили для кнопок действий */
.btn-group .btn {
  margin-right: 0;
}

/* Стили для производственных линий */
.production-line-card {
  border-radius: 1rem;
  transition: all 0.3s ease;
  min-height: 500px; /* Минимальная высота для всех карточек */
  display: flex;
  flex-direction: column;
}

.production-line-card .card-body {
  flex: 1; /* Растягиваем тело карточки */
  display: flex;
  flex-direction: column;
}

.production-line-card .card-footer {
  margin-top: auto; /* Прижимаем footer к низу */
}

/* Фиксированная высота для секции статистики */
.production-line-card .row.text-center {
  margin-top: auto;
  padding-top: 1rem;
}

/* Фиксированная высота для секции с текущим заказом */
.production-line-card .alert {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.production-line-active {
  border-color: #1cc88a;
  background: linear-gradient(135deg, #1cc88a 0%, #17a2b8 100%);
  color: white;
}

.production-line-stopped {
  border-color: #6c757d;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
}

/* Обеспечиваем равную высоту колонок */
.row.production-lines-row {
  display: flex;
  flex-wrap: wrap;
}

.row.production-lines-row > [class*="col-"] {
  display: flex;
  margin-bottom: 1.5rem;
}

/* Стили для dropdown в карточках */
.production-line-card .dropdown-menu {
  z-index: 1050;
}

/* Стили для материалов с низким запасом */
.low-stock-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Стили для мобильных устройств */
@media (max-width: 768px) {
  .card-header {
    padding: 0.75rem 1rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    margin-bottom: 0.25rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  /* Адаптивные стили для производственных линий */
  .production-line-card {
    min-height: auto; /* Убираем фиксированную высоту на мобильных */
  }

  .row.production-lines-row > [class*="col-"] {
    margin-bottom: 1rem;
  }

  /* Уменьшаем отступы в карточках на мобильных */
  .production-line-card .card-body {
    padding: 1rem 0.75rem;
  }

  .production-line-card .alert {
    min-height: auto;
    padding: 0.75rem;
  }

  /* Обеспечиваем видимость кнопок на мобильных устройствах */
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.justify-content-between .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  /* Улучшаем отображение фильтров на мобильных */
  .card .row.g-3 {
    margin: 0;
  }

  .card .row.g-3 > [class*="col-"] {
    padding: 0.25rem;
    margin-bottom: 0.5rem;
  }
}

/* Стили для Dashboard */
.dashboard-card {
  border-radius: 1rem;
  overflow: hidden;
}

.dashboard-stat {
  font-size: 2rem;
  font-weight: bold;
}

/* Стили для статистических карточек */
.border-left-primary,
.border-left-success,
.border-left-info,
.border-left-warning,
.border-left-danger,
.border-left-secondary {
  border-radius: 0.5rem;
  transition: transform 0.2s ease-in-out;
}

.border-left-primary:hover,
.border-left-success:hover,
.border-left-info:hover,
.border-left-warning:hover,
.border-left-danger:hover,
.border-left-secondary:hover {
  transform: translateY(-2px);
}

/* Обеспечиваем равную высоту статистических карточек */
.row.mb-4 > [class*="col-"] .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.row.mb-4 > [class*="col-"] .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Стили для форм */
.form-control:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Стили для навигации */
.navbar-brand {
  font-size: 1.25rem;
}

.dropdown-menu {
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Улучшенные стили для навигации */
.navbar-nav .nav-link {
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin: 0 0.25rem;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

/* Стили для dropdown в навигации */
.navbar-nav .dropdown-toggle::after {
  display: none; /* Скрываем стандартную стрелку Bootstrap */
}

.navbar-nav .dropdown:hover .dropdown-menu {
  display: block;
  margin-top: 0;
}

.navbar-nav .dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 220px;
}

.navbar-nav .dropdown-item {
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.navbar-nav .dropdown-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.navbar-nav .dropdown-header {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Индикаторы активного состояния */
.navbar-nav .nav-item.active .nav-link {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

/* Адаптивные стили для мобильных устройств */
@media (max-width: 991px) {
  .navbar-nav .nav-link {
    margin: 0.25rem 0;
    padding: 0.75rem 1rem;
  }

  .navbar-nav .dropdown:hover .dropdown-menu {
    display: none; /* Отключаем hover на мобильных */
  }

  .navbar-nav .dropdown-menu {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Улучшенная адаптивность для кнопок и фильтров */
  .page-header {
    flex-direction: column;
    align-items: stretch !important;
    gap: 1rem;
  }

  .page-header .d-flex {
    justify-content: center;
  }

  .page-header .btn {
    min-width: auto;
    flex: 1;
  }

  /* Адаптивные фильтры */
  .filter-card .row.g-3 > [class*="col-"] {
    margin-bottom: 1rem;
  }

  .filter-card .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .filter-card .btn {
    width: 100%;
  }
}

/* Стили для footer */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

/* Дополнительные стили для улучшения отображения */
@media (min-width: 1200px) {
  .production-line-card {
    min-height: 450px; /* Немного меньше на больших экранах */
  }
}

@media (max-width: 991px) {
  .production-line-card {
    min-height: 400px; /* Еще меньше на планшетах */
  }
}

/* Стили для предотвращения переполнения текста */
.production-line-card .card-header h6 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.production-line-card .alert-heading {
  font-size: 0.9rem;
}

/* Стили для кнопок в карточках */
.production-line-card .btn-sm {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

/* Улучшенные стили для статистики в карточках */
.production-line-card .border-end:last-child {
  border-right: none !important;
}

/* Стили для обеспечения корректного отображения dropdown */
.production-line-card .dropdown {
  position: relative;
}

.production-line-card .dropdown-menu {
  position: absolute;
  top: 100%;
  left: auto;
  right: 0;
  z-index: 1050;
  min-width: 160px;
}

/* Улучшенные стили для кнопок создания и фильтров */
.page-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e3e6f0;
}

.page-header .btn {
  min-width: 150px;
  font-weight: 500;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.page-header .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Стили для карточек фильтров */
.filter-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 0.75rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.filter-card .card-body {
  padding: 1.5rem;
}

.filter-card .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.filter-card .btn {
  min-width: 100px;
  font-weight: 500;
}

/* Обеспечиваем видимость элементов на всех экранах */
.btn, .form-control, .form-select {
  position: relative;
  z-index: 1;
}

/* Стили для улучшения контрастности */
.btn-primary {
  background-color: #007bff !important;
  border: 2px solid #007bff !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

.btn-primary:hover {
  background-color: #0056b3 !important;
  border-color: #0056b3 !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 86, 179, 0.4) !important;
}

.btn-outline-primary {
  border: 2px solid #007bff !important;
  color: #007bff !important;
  font-weight: 600 !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2) !important;
}

.btn-outline-primary:hover {
  background-color: #007bff !important;
  border-color: #007bff !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3) !important;
}

/* Дополнительные стили для обеспечения видимости */
.btn {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  text-decoration: none !important;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  transition: all 0.15s ease-in-out;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Стили для обеспечения видимости на светлом фоне */
.card {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control, .form-select {
  background-color: #ffffff;
  border: 1px solid #d1d3e2;
  border-radius: 0.35rem;
}

.form-control:focus, .form-select:focus {
  background-color: #ffffff;
  border-color: #4e73df;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Улучшенные стили для dropdown */
.dropdown-menu {
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  z-index: 1000;
}

.dropdown-item {
  color: #3a3b45;
  text-decoration: none;
  background-color: transparent;
  border: 0;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  text-align: inherit;
  white-space: nowrap;
  border-radius: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b;
  background-color: #f8f9fa;
}

/* Дополнительные стили для очень маленьких экранов */
@media (max-width: 576px) {
  .page-header h1 {
    font-size: 1.5rem;
  }

  .page-header .btn {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  .filter-card .card-body {
    padding: 1rem;
  }

  .filter-card .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .filter-card .btn {
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  /* Стек кнопок на очень маленьких экранах */
  .d-flex.gap-2 {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  .btn-group {
    flex-direction: column;
    width: 100%;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
}

/* Стили для обеспечения видимости на всех устройствах */
@media (min-width: 576px) and (max-width: 767px) {
  .page-header .d-flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .filter-card .row.g-3 > [class*="col-"] {
    margin-bottom: 0.75rem;
  }
}

/* Улучшенные стили для планшетов */
@media (min-width: 768px) and (max-width: 991px) {
  .page-header .btn {
    min-width: 120px;
  }

  .filter-card .btn {
    min-width: 80px;
  }
}

/* Принудительная видимость для всех кнопок */
.btn, .btn-primary, .btn-outline-primary, .btn-outline-secondary, .btn-outline-info {
  visibility: visible !important;
  opacity: 1 !important;
  display: inline-flex !important;
  position: relative !important;
  z-index: 100 !important;
  min-height: 38px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

/* Обеспечиваем контрастность текста */
.btn-primary {
  color: #ffffff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-outline-primary {
  color: #007bff !important;
  background-color: #ffffff !important;
  border-color: #007bff !important;
}

.btn-outline-secondary {
  color: #6c757d !important;
  background-color: #ffffff !important;
  border: 2px solid #6c757d !important;
  font-weight: 600 !important;
}

.btn-outline-info {
  color: #17a2b8 !important;
  background-color: #ffffff !important;
  border: 2px solid #17a2b8 !important;
  font-weight: 600 !important;
}