@model ProductionManagementSystem.Models.Product
@{
    ViewData["Title"] = "Создать продукт";
}

<div class="d-flex justify-content-between align-items-center mb-4 page-header">
    <h1 class="h2 mb-0">
        <i class="bi bi-plus-circle me-2 text-primary"></i>Создать новый продукт
    </h1>
    <div class="d-flex gap-2">
        <a asp-action="Index" class="btn btn-outline-secondary shadow-sm">
            <i class="bi bi-arrow-left me-2"></i>Назад к списку
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-box-seam me-2"></i>Информация о продукте
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Name" class="form-label">
                                <i class="bi bi-tag me-1"></i>Название продукта
                            </label>
                            <input asp-for="Name" class="form-control shadow-sm" placeholder="Введите название продукта" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Category" class="form-label">
                                <i class="bi bi-folder me-1"></i>Категория
                            </label>
                            <input asp-for="Category" class="form-control shadow-sm" placeholder="Введите категорию" />
                            <span asp-validation-for="Category" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Description" class="form-label">
                            <i class="bi bi-file-text me-1"></i>Описание
                        </label>
                        <textarea asp-for="Description" class="form-control shadow-sm" rows="3" 
                                  placeholder="Введите описание продукта"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Specifications" class="form-label">
                            <i class="bi bi-gear me-1"></i>Технические характеристики
                        </label>
                        <textarea asp-for="Specifications" class="form-control shadow-sm" rows="2" 
                                  placeholder="Введите технические характеристики"></textarea>
                        <span asp-validation-for="Specifications" class="text-danger"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="ProductionTimePerUnit" class="form-label">
                                <i class="bi bi-clock me-1"></i>Время производства (мин/ед)
                            </label>
                            <input asp-for="ProductionTimePerUnit" class="form-control shadow-sm" 
                                   type="number" step="0.1" min="0" placeholder="0.0" />
                            <span asp-validation-for="ProductionTimePerUnit" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="MinimalStock" class="form-label">
                                <i class="bi bi-exclamation-triangle me-1"></i>Минимальный запас
                            </label>
                            <input asp-for="MinimalStock" class="form-control shadow-sm" 
                                   type="number" min="0" placeholder="0" />
                            <span asp-validation-for="MinimalStock" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary shadow-sm">
                            <i class="bi bi-check-circle me-2"></i>Создать продукт
                        </button>
                        <a asp-action="Index" class="btn btn-outline-secondary shadow-sm">
                            <i class="bi bi-x-circle me-2"></i>Отмена
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="bi bi-archive me-2"></i>Материалы для производства
                </h6>
            </div>
            <div class="card-body">
                <div id="materials-section">
                    <p class="text-muted mb-3">
                        <i class="bi bi-info-circle me-1"></i>
                        Выберите материалы, необходимые для производства этого продукта
                    </p>
                    
                    <div id="material-requirements">
                        <!-- Материалы будут добавляться динамически -->
                    </div>

                    <button type="button" class="btn btn-outline-info btn-sm" onclick="addMaterialRequirement()">
                        <i class="bi bi-plus-circle me-1"></i>Добавить материал
                    </button>
                </div>
            </div>
        </div>

        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="bi bi-lightbulb me-2"></i>Подсказки
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        Укажите точное время производства для планирования
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        Добавьте все необходимые материалы
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        Установите минимальный запас для контроля
                    </li>
                    <li>
                        <i class="bi bi-check text-success me-2"></i>
                        Используйте понятные названия и описания
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        let materialIndex = 0;
        const materials = @Html.Raw(Json.Serialize(ViewBag.Materials));

        function addMaterialRequirement() {
            const container = document.getElementById('material-requirements');
            const div = document.createElement('div');
            div.className = 'mb-3 material-requirement';
            div.innerHTML = `
                <div class="row">
                    <div class="col-8">
                        <select name="materialIds[${materialIndex}]" class="form-select form-select-sm">
                            <option value="">Выберите материал</option>
                            ${materials.map(m => `<option value="${m.value}">${m.text}</option>`).join('')}
                        </select>
                    </div>
                    <div class="col-3">
                        <input name="quantities[${materialIndex}]" type="number" step="0.01" min="0" 
                               class="form-control form-control-sm" placeholder="Кол-во" />
                    </div>
                    <div class="col-1">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeMaterialRequirement(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(div);
            materialIndex++;
        }

        function removeMaterialRequirement(button) {
            button.closest('.material-requirement').remove();
        }

        // Добавляем первый материал при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            if (materials && materials.length > 0) {
                addMaterialRequirement();
            }
        });
    </script>
}
